from typing import Any, Dict, List, Sequence, Set, Tuple, Union

from sqlalchemy import and_
from sqlalchemy.engine import Row
from sqlalchemy.orm import Session

from holmatro_customer_portal.database.enums import LanguageEnum
from holmatro_customer_portal.database.models import Attribute, AttributeTranslation, Language
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class AttributeTranslationService:
    """Service for attribute translation queries using ORM."""

    def __init__(self, session: Session):
        self.session = session

    def get_attribute_translations(
        self, attribute_ids: List[int], language_codes: Set[str]
    ) -> Dict[int, Dict[str, str | None]]:
        """
        Optimized method to retrieve attribute translations for given attribute_ids and language_codes.

        Args:
            attribute_ids: List of attribute IDs to get translations for
            language_codes: Set of language codes to fetch translations for

        Returns:
            Dictionary mapping attribute IDs to translation info (title, unit, etc.)
        """
        if not attribute_ids or not language_codes:
            return {}

        # Get language IDs for the query
        language_id_map = {
            lang.language_code: lang.id
            for lang in self.session.query(Language).filter(Language.language_code.in_(language_codes)).all()
        }

        if not language_id_map:
            return {}

        language_ids = list(language_id_map.values())

        # Get attribute metadata
        results = (
            self.session.query(
                Attribute.attribute_id,
                AttributeTranslation.name,
                AttributeTranslation.attribute_unit,
                AttributeTranslation.attribute_value,
                Language.language_code,
            )
            .join(Attribute)
            .join(Language)
            .filter(
                and_(
                    AttributeTranslation.language_id.in_(language_ids),
                    Attribute.attribute_id.in_(attribute_ids),
                    AttributeTranslation.name.isnot(None),
                )
            )
            .order_by(Attribute.attribute_id, Language.language_code)
            .all()
        )

        return self._process_translation_results(results, language_codes)

    @staticmethod
    def _process_translation_results(
        results: Union[Sequence[Tuple[Any, ...]], Sequence[Row[Any]]], language_codes: Set[str]
    ) -> Dict[int, Dict[str, str | None]]:
        """Process the raw query results into the expected format."""
        attribute_id_to_info_map: Dict[int, Dict[str, str | None]] = {}

        for attribute_id, name, unit, attribute_value, lang_code in results:
            if attribute_id not in attribute_id_to_info_map:
                attribute_id_to_info_map[attribute_id] = {
                    "unit": None,
                    "title": None,
                    "attribute_value": None,
                }

            # Always set the unit from any language, as it should be consistent
            attribute_id_to_info_map[attribute_id]["unit"] = unit

            # Store attribute_value (should be consistent across languages for the same attribute)
            if attribute_value:
                attribute_id_to_info_map[attribute_id]["attribute_value"] = attribute_value

            # Use the fetched language for title (user's preferred language)
            attribute_id_to_info_map[attribute_id]["title"] = name

        return attribute_id_to_info_map

    def get_attribute_value_translations(
        self, attribute_ids: List[int], attribute_values: List[str], language_codes: Set[str]
    ) -> Dict[Tuple[int, str], str]:
        """
        Get translated attribute values for specific attribute IDs and raw values.

        This method retrieves the translated attribute_value field from AttributeTranslation
        for specific combinations of attribute_id and raw attribute values.

        Args:
            attribute_ids: List of attribute IDs to get translations for
            attribute_values: List of raw attribute values (from OpenSearch buckets)
            language_codes: Set of language codes to fetch translations for

        Returns:
            Dictionary mapping (attribute_id, raw_value) tuples to translated values
        """
        if not attribute_ids or not attribute_values or not language_codes:
            return {}

        # Get language IDs for the query
        language_id_map = {
            lang.language_code: lang.id
            for lang in self.session.query(Language).filter(Language.language_code.in_(language_codes)).all()
        }

        if not language_id_map:
            return {}

        language_ids = list(language_id_map.values())

        # Query for attribute value translations
        # We need to match both the attribute_id and the raw content value
        results = (
            self.session.query(
                Attribute.attribute_id,
                Attribute.content,
                AttributeTranslation.attribute_value,
                Language.language_code,
            )
            .join(AttributeTranslation, Attribute.translations)
            .join(Language)
            .filter(
                and_(
                    AttributeTranslation.language_id.in_(language_ids),
                    Attribute.attribute_id.in_(attribute_ids),
                    Attribute.content.in_(attribute_values),
                    AttributeTranslation.attribute_value.isnot(None),
                )
            )
            .distinct()
            .all()
        )

        # Process attribute value translation results
        value_translations: Dict[Tuple[int, str], str] = {}
        for attribute_id, raw_content, translated_value, lang_code in results:
            # Since we only fetch one language, use whatever we get
            key = (attribute_id, raw_content)
            value_translations[key] = translated_value

        return value_translations
