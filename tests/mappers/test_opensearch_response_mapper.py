from unittest.mock import Mock

import pytest

from holmatro_customer_portal.database.enums import DBFilterType, LanguageEnum
from holmatro_customer_portal.mappers.opensearch_response_mapper import OpenSearchResponseMapper
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import FilterAttribute


class TestOpenSearchResponseMapper:
    """Unit tests for OpenSearchResponseMapper methods that create FilterAttribute objects."""

    @pytest.fixture
    def mapper(self):
        """Create a mapper instance with mocked dependencies."""
        mock_session = Mock()
        mapper = OpenSearchResponseMapper(session=mock_session)
        # Replace the real service with a mock for testing
        mapper.attribute_translation_service = Mock()
        return mapper

    def test_extract_filter_options_from_bucket_english_string_attributes(self, mapper):
        """Test that string attributes for English users create FilterAttribute with both title and display_name."""
        # Arrange
        attribute_bucket = {
            "values_string": {
                "buckets": [
                    {"key": "red", "doc_count": 5},
                    {"key": "blue", "doc_count": 3},
                ]
            }
        }
        attribute_id = 100
        user_language_code = LanguageEnum.ENGLISH.value

        # Act
        result = mapper._extract_filter_options_from_bucket(
            attribute_bucket, attribute_id, DBFilterType.CHECKBOX, user_language_code
        )

        # Assert
        assert len(result) == 2

        # Check first FilterAttribute
        assert isinstance(result[0], FilterAttribute)
        assert result[0].title == "red"
        assert result[0].display_name == "red"  # Same as title for English
        assert result[0].is_selected is False

        # Check second FilterAttribute
        assert isinstance(result[1], FilterAttribute)
        assert result[1].title == "blue"
        assert result[1].display_name == "blue"  # Same as title for English
        assert result[1].is_selected is False

    def test_extract_filter_options_from_bucket_non_english_string_attributes(self, mapper):
        """Test that string attributes for non-English users use translations for display_name."""
        # Arrange
        attribute_bucket = {
            "values_string": {
                "buckets": [
                    {"key": "red", "doc_count": 5},
                    {"key": "blue", "doc_count": 3},
                ]
            }
        }
        attribute_id = 100
        user_language_code = "nl"

        # Mock the translation service to return Dutch translations
        mock_translations = {
            (100, "red"): "rood",
            (100, "blue"): "blauw",
        }
        mapper.attribute_translation_service.get_attribute_value_translations.return_value = mock_translations

        # Act
        result = mapper._extract_filter_options_from_bucket(
            attribute_bucket, attribute_id, DBFilterType.CHECKBOX, user_language_code
        )

        # Assert
        assert len(result) == 2

        # Check first FilterAttribute
        assert isinstance(result[0], FilterAttribute)
        assert result[0].title == "red"  # Original English value
        assert result[0].display_name == "rood"  # Translated Dutch value
        assert result[0].is_selected is False

        # Check second FilterAttribute
        assert isinstance(result[1], FilterAttribute)
        assert result[1].title == "blue"  # Original English value
        assert result[1].display_name == "blauw"  # Translated Dutch value
        assert result[1].is_selected is False

        # Verify translation service was called correctly
        mapper.attribute_translation_service.get_attribute_value_translations.assert_called_once_with(
            [100], ["red", "blue"], {"nl"}
        )

    def test_extract_filter_options_from_bucket_numeric_attributes(self, mapper):
        """Test that numeric attributes create FilterAttribute objects with min/max values."""
        # Arrange
        attribute_bucket = {
            "values_numeric_stats": {
                "count": 10,
                "min": 5.5,
                "max": 25.0,
            }
        }
        attribute_id = 200
        user_language_code = "en"

        # Act
        result = mapper._extract_filter_options_from_bucket(
            attribute_bucket, attribute_id, DBFilterType.SLIDER, user_language_code
        )

        # Assert
        assert len(result) == 2

        # Check min value FilterAttribute
        assert isinstance(result[0], FilterAttribute)
        assert result[0].title == "5.5"
        assert result[0].display_name == "5.5"  # Same for numeric values
        assert result[0].is_selected is False

        # Check max value FilterAttribute
        assert isinstance(result[1], FilterAttribute)
        assert result[1].title == "25.0"
        assert result[1].display_name == "25.0"  # Same for numeric values
        assert result[1].is_selected is False

    def test_extract_filter_options_from_bucket_no_values(self, mapper):
        """Test that empty buckets return empty list."""
        # Arrange
        attribute_bucket = {}
        attribute_id = 300
        user_language_code = "en"

        # Act
        result = mapper._extract_filter_options_from_bucket(
            attribute_bucket, attribute_id, DBFilterType.CHECKBOX, user_language_code
        )

        # Assert
        assert result == []

    def test_extract_filter_options_from_bucket_translation_fallback(self, mapper):
        """Test that missing translations fall back to original values."""
        # Arrange
        attribute_bucket = {
            "values_string": {
                "buckets": [
                    {"key": "red", "doc_count": 5},
                    {"key": "unknown_color", "doc_count": 1},
                ]
            }
        }
        attribute_id = 100
        user_language_code = "nl"

        # Mock translation service to return partial translations
        mock_translations = {
            (100, "red"): "rood",
            # "unknown_color" has no translation
        }
        mapper.attribute_translation_service.get_attribute_value_translations.return_value = mock_translations

        # Act
        result = mapper._extract_filter_options_from_bucket(
            attribute_bucket, attribute_id, DBFilterType.CHECKBOX, user_language_code
        )

        # Assert
        assert len(result) == 2

        # Check translated value
        assert result[0].title == "red"
        assert result[0].display_name == "rood"

        # Check fallback value (no translation available)
        assert result[1].title == "unknown_color"
        assert result[1].display_name == "unknown_color"  # Falls back to original
