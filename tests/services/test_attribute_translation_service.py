import uuid
from unittest.mock import MagicMock, patch

from holmatro_customer_portal.database.models import Language
from holmatro_customer_portal.services.attribute_translation_service import AttributeTranslationService
from tests.fixtures.database import session


class TestAttributeTranslationService:
    """Test cases for AttributeTranslationService optimization."""

    def test_get_attribute_translations_with_mock_data(self, session):
        """Test the ORM query with mocked database results."""
        service = AttributeTranslationService(session)

        # Mock Language query results
        mock_languages = [
            MagicMock(language_code="en", id=uuid.uuid4()),
            MagicMock(language_code="nl", id=uuid.uuid4()),
        ]

        # Mock the ORM query results (now includes attribute_value)
        # Since we only fetch one language now, we'll only get Dutch results
        mock_orm_result = [
            (123, "Test Attribuut", "kg", "Rood", "nl"),
            (456, "Another Attribute", "m", None, "en"),  # This one only has English
        ]

        # Mock the session.query chain
        mock_query = MagicMock()
        mock_query.filter.return_value.all.return_value = mock_languages

        mock_attr_query = MagicMock()
        mock_attr_query.join.return_value.join.return_value.filter.return_value.order_by.return_value.all.return_value = (
            mock_orm_result
        )

        with patch.object(session, "query") as mock_session_query:
            # First call returns language query, second call returns attribute query
            mock_session_query.side_effect = [mock_query, mock_attr_query]

            result = service.get_attribute_translations([123, 456], {"nl"})  # Only fetch Dutch

            # Verify the result structure
            assert 123 in result
            assert 456 in result

            # Check attribute 123 (Dutch translation)
            assert result[123]["unit"] == "kg"
            assert result[123]["title"] == "Test Attribuut"  # Dutch name
            assert result[123]["attribute_value"] == "Rood"  # Dutch attribute value
            # url_key is no longer returned from translation service

            # Check attribute 456 (only English available, but we requested Dutch)
            assert result[456]["unit"] == "m"
            assert result[456]["title"] == "Another Attribute"  # English fallback
            assert result[456]["attribute_value"] is None  # No attribute value

    def test_get_attribute_translations_empty_inputs(self, session):
        """Test that empty inputs return empty results."""
        service = AttributeTranslationService(session)

        # Test empty attribute_ids
        result = service.get_attribute_translations([], {"en", "nl"})
        assert result == {}

        # Test empty language_codes
        result = service.get_attribute_translations([123, 456], set())
        assert result == {}

        # Test both empty
        result = service.get_attribute_translations([], set())
        assert result == {}

    def test_process_translation_results_english_only(self, session):
        """Test result processing with English only."""
        service = AttributeTranslationService(session)

        results = [
            (123, "Test Attribute", "kg", "Red", "en"),
            (456, "Another Attribute", "m", None, "en"),
        ]

        processed = service._process_translation_results(results, {"en"})

        assert len(processed) == 2
        assert processed[123]["title"] == "Test Attribute"  # English used for title
        assert processed[123]["attribute_value"] == "Red"  # English attribute value
        assert processed[456]["title"] == "Another Attribute"
        assert processed[456]["attribute_value"] is None  # No attribute value
        # url_key is no longer returned from translation service

    def test_process_translation_results_multilingual(self, session):
        """Test result processing with multiple languages."""
        service = AttributeTranslationService(session)

        # Since we only fetch one language now, simulate Dutch-only results
        results = [
            (123, "Test Attribuut", "kg", "Rood", "nl"),
            (456, "Another Attribute", "m", None, "en"),  # English fallback
        ]

        processed = service._process_translation_results(results, {"nl"})

        assert len(processed) == 2

        # Attribute 123 in Dutch
        assert processed[123]["title"] == "Test Attribuut"  # Dutch
        assert processed[123]["unit"] == "kg"
        assert processed[123]["attribute_value"] == "Rood"  # Dutch attribute value

        # Attribute 456 in English (fallback)
        assert processed[456]["title"] == "Another Attribute"
        assert processed[456]["unit"] == "m"
        assert processed[456]["attribute_value"] is None  # No attribute value
        # url_key is no longer returned from translation service

    def test_get_attribute_translations_no_languages_found(self, session):
        """Test behavior when no languages are found in database."""
        service = AttributeTranslationService(session)

        # Mock Language query to return empty list
        mock_query = MagicMock()
        mock_query.filter.return_value.all.return_value = []

        with patch.object(session, "query", return_value=mock_query):
            result = service.get_attribute_translations([123, 456], {"en", "nl"})
            assert result == {}

    def test_get_attribute_value_translations(self, session):
        """Test the separate method for attribute value translations."""
        service = AttributeTranslationService(session)

        # Mock Language query results
        mock_languages = [
            MagicMock(language_code="en", id=uuid.uuid4()),
            MagicMock(language_code="nl", id=uuid.uuid4()),
        ]

        # Mock the attribute value translation query results
        mock_value_result = [
            (123, "red", "Red", "en"),
            (123, "red", "Rood", "nl"),
            (456, "blue", "Blue", "en"),
        ]

        # Setup mock queries
        mock_lang_query = MagicMock()
        mock_lang_query.filter.return_value.all.return_value = mock_languages

        mock_value_query = MagicMock()
        mock_value_query.join.return_value.join.return_value.filter.return_value.distinct.return_value.all.return_value = (
            mock_value_result
        )

        with patch.object(session, "query") as mock_session_query:
            # First call returns language query, second call returns attribute value query
            mock_session_query.side_effect = [mock_lang_query, mock_value_query]

            result = service.get_attribute_value_translations([123, 456], ["red", "blue"], {"en", "nl"})

            # Verify the result structure
            assert (123, "red") in result
            assert (456, "blue") in result

            # Check that Dutch translation is preferred over English
            assert result[(123, "red")] == "Rood"  # Dutch translation
            assert result[(456, "blue")] == "Blue"  # Only English available

    def test_get_attribute_value_translations_empty_inputs(self, session):
        """Test that empty inputs return empty results for value translations."""
        service = AttributeTranslationService(session)

        # Test empty attribute_ids
        result = service.get_attribute_value_translations([], ["red", "blue"], {"en", "nl"})
        assert result == {}

        # Test empty attribute_values
        result = service.get_attribute_value_translations([123, 456], [], {"en", "nl"})
        assert result == {}

        # Test empty language_codes
        result = service.get_attribute_value_translations([123, 456], ["red", "blue"], set())
        assert result == {}
